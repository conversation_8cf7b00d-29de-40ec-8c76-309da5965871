{"name": "Forecaster - Weather Planning", "short_name": "Forecaster", "description": "A premium weather planning application for outdoor activities. Upload GPX files, analyze weather conditions along your path, and make informed decisions for your adventures.", "start_url": "/", "display": "standalone", "background_color": "#ffffff", "theme_color": "#3b82f6", "orientation": "portrait-primary", "scope": "/", "lang": "en", "categories": ["weather", "sports", "travel", "utilities"], "icons": [{"src": "/icons/icon-72x72.png", "sizes": "72x72", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-96x96.png", "sizes": "96x96", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-128x128.png", "sizes": "128x128", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-144x144.png", "sizes": "144x144", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-152x152.png", "sizes": "152x152", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-384x384.png", "sizes": "384x384", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "maskable any"}], "screenshots": [{"src": "/screenshots/desktop-1.png", "sizes": "1280x720", "type": "image/png", "platform": "wide", "label": "Weather forecast dashboard"}, {"src": "/screenshots/mobile-1.png", "sizes": "390x844", "type": "image/png", "platform": "narrow", "label": "Mobile weather planning"}], "shortcuts": [{"name": "Upload GPX", "short_name": "Upload", "description": "Upload a new GPX file for weather analysis", "url": "/?action=upload", "icons": [{"src": "/icons/upload-96x96.png", "sizes": "96x96", "type": "image/png"}]}, {"name": "Settings", "short_name": "Settings", "description": "Manage your forecast settings", "url": "/?action=settings", "icons": [{"src": "/icons/settings-96x96.png", "sizes": "96x96", "type": "image/png"}]}], "related_applications": [], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "navigate-existing"}}