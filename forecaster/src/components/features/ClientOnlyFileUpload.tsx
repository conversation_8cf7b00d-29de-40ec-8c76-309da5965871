"use client";

import dynamic from 'next/dynamic';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Upload } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

// Loading component that matches the server-rendered structure
function FileUploadSkeleton({ className }: { className?: string }) {
  return (
    <Card className={cn('w-full', className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Upload className="h-5 w-5" />
          Upload GPX File
        </CardTitle>
        <CardDescription>
          Upload your GPX file to get started with weather analysis
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="border-2 border-dashed rounded-lg p-8 text-center transition-colors border-muted-foreground/25">
          <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
          <p className="text-sm text-muted-foreground mb-4">
            Drag and drop your GPX file here, or click to browse
          </p>
          <Button variant="outline" disabled>
            Choose File
          </Button>
          <p className="text-xs text-muted-foreground mt-4">
            Supports GPX files up to 10 MB
          </p>
        </div>
      </CardContent>
    </Card>
  );
}

// Dynamically import the actual FileUpload component with no SSR
const DynamicFileUpload = dynamic(
  () => import('./FileUpload').then(mod => ({ default: mod.FileUpload })),
  {
    ssr: false,
    loading: () => <FileUploadSkeleton />
  }
);

export { DynamicFileUpload as FileUpload };
